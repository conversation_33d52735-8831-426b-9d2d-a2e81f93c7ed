import { Router } from "express";

import {
  initializePayment,
  verifyPayment,
  handleWebhook,
  getBanks,
  verifyAccountNumber,
  getPaymentStats,
} from "~/controllers/payments";
import { verifyRequest } from "~/middlewares/auth";

const paymentsRouter = Router();

// Public routes (no authentication required)
paymentsRouter.post("/webhook", handleWebhook);
paymentsRouter.get("/banks", getBanks);
paymentsRouter.post("/verify-account", verifyAccountNumber);

// User routes (authentication required)
paymentsRouter.post(
  "/initialize",
  verifyRequest({
    isVerified: true,
    isDeleted: false,
    allowedTypes: ["ACCESS"],
    allowedStatus: ["APPROVED"],
    allowedRoles: ["USER"],
  }),
  initializePayment,
);

paymentsRouter.get(
  "/verify/:reference",
  verifyRequest({
    isVerified: true,
    isDeleted: false,
    allowedTypes: ["ACCESS"],
    allowedStatus: ["APPROVED"],
    allowedRoles: ["USER"],
  }),
  verifyPayment,
);

// Admin routes
paymentsRouter.get(
  "/stats",
  verifyRequest({
    isVerified: true,
    isDeleted: false,
    allowedTypes: ["ACCESS"],
    allowedStatus: ["APPROVED"],
    allowedRoles: ["SUPER_ADMIN", "ADMIN"],
  }),
  getPaymentStats,
);

export { paymentsRouter };
