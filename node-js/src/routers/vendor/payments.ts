import { Router } from "express";

import {
  createSubaccount,
  getSubaccount,
  getPaymentStats,
  getPayoutHistory,
  getTransactionHistory,
} from "~/controllers/vendor/payments";

const paymentsRouter = Router();

// Vendor payment routes
paymentsRouter.post("/subaccount", createSubaccount);
paymentsRouter.get("/subaccount", getSubaccount);
paymentsRouter.get("/stats", getPaymentStats);
paymentsRouter.get("/payouts", getPayoutHistory);
paymentsRouter.get("/transactions", getTransactionHistory);

export { paymentsRouter };
