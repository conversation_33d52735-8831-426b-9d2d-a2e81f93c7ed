import "dotenv/config";

import * as zod from "zod";

const envSchema = zod.object({
  PORT: zod.coerce.number().min(1000).max(9999),
  DATABASE_URL: zod.string().url(),
  JWT_SECRET: zod.string(),
  JWT_EXPIRY: zod.string(),
  NODEMAILER_HOST: zod.string(),
  NODEMAILER_PORT: zod.coerce.number(),
  NODEMAILER_SECURE: zod.preprocess(
    (val) => (val === "true" ? true : val === "false" ? false : val),
    zod.boolean({
      message: "NODEMAILER_SECURE must be a boolean",
    })
  ),
  NODEMAILER_EMAIL: zod.string().email(),
  NODEMAILER_PASSWORD: zod.string(),
  CLIENT_BASE_URL: zod.string().url(),
  APP_NAME: zod.string(),
  APP_SUPPORT_EMAIL: zod.string().email(),
  APP_ADMIN_EMAIL: zod.string().email(),
  AWS_ACCESS_KEY_ID: zod.string(),
  AWS_SECRET_ACCESS_KEY: zod.string(),
  AWS_BUCKET: zod.string(),
  AWS_REGION: zod.string(),
  PAYSTACK_SECRET_KEY: zod.string(),
  PAYSTACK_PUBLIC_KEY: zod.string(),
  PAYSTACK_WEBHOOK_SECRET: zod.string().optional(),
});

export const env = envSchema.parse(process.env);
