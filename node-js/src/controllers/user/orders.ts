import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import { initializePaymentService } from "~/services/payments";
import {
  createOrderService,
  getOrderService,
  getOrdersService,
} from "~/services/user/orders";
import {
  createOrderBodySchema,
  getOrderParamsSchema,
  getOrdersQuerySchema,
} from "~/validators/user/orders";

async function getOrders(request: Request, response: Response) {
  try {
    const {
      page,
      limit,
      sort,
      status,
      productName,
      vendorName,
      minTotalPrice,
      maxTotalPrice,
      categoryId,
    } = getOrdersQuerySchema.parse(request.query);

    const {
      orders,
      total,
      pages,
      limit: responseLimit,
      page: responsePage,
    } = await getOrdersService({
      userId: request.user.id,
      page,
      limit,
      sort,
      status,
      productName,
      vendorName,
      minTotalPrice,
      maxTotalPrice,
      categoryId,
    });

    return response.success(
      {
        data: { orders },
        meta: { total, pages, limit: responseLimit, page: responsePage },
      },
      {
        message: "Orders fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function getOrder(request: Request, response: Response) {
  try {
    const { id } = getOrderParamsSchema.parse(request.params);

    const { order } = await getOrderService({
      userId: request.user.id,
      orderId: id,
    });

    return response.success(
      {
        data: { order },
      },
      {
        message: "Order fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function createOrder(request: Request, response: Response) {
  try {
    const { products, deliveryOption, customerEmail } =
      createOrderBodySchema.parse(request.body);

    // Initialize payment instead of creating order directly
    const result = await initializePaymentService({
      userId: request.user.id,
      products,
      deliveryOption,
      customerEmail,
    });

    return response.success(
      {
        data: result,
      },
      {
        message:
          "Payment initialized successfully. Complete payment to confirm order.",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getOrders, getOrder, createOrder };
