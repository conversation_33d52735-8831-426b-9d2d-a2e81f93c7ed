import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  createVendorSubaccountService,
  getVendorSubaccountService,
  getVendorPaymentStatsService,
  getVendorPayoutHistoryService,
  getVendorTransactionHistoryService,
} from "~/services/vendor/paystack";

/**
 * Create Paystack subaccount for vendor
 */
export async function createSubaccount(request: Request, response: Response) {
  try {
    const { businessName, settlementBank, accountNumber } = request.body;
    const vendorAuthId = request.user.id;

    if (!businessName || !settlementBank || !accountNumber) {
      return response.badRequest(
        {},
        { message: "Business name, settlement bank, and account number are required" },
      );
    }

    const result = await createVendorSubaccountService(vendorAuthId, {
      businessName,
      settlementBank,
      accountNumber,
    });

    return response.success(
      { data: result },
      { message: "Paystack account connected successfully" },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get vendor's Paystack subaccount details
 */
export async function getSubaccount(request: Request, response: Response) {
  try {
    const vendorAuthId = request.user.id;

    const subaccount = await getVendorSubaccountService(vendorAuthId);

    return response.success(
      { data: { subaccount } },
      { message: "Subaccount details fetched successfully" },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get vendor payment statistics
 */
export async function getPaymentStats(request: Request, response: Response) {
  try {
    const vendorAuthId = request.user.id;

    const stats = await getVendorPaymentStatsService(vendorAuthId);

    return response.success(
      { data: stats },
      { message: "Payment statistics fetched successfully" },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get vendor's payout history
 */
export async function getPayoutHistory(request: Request, response: Response) {
  try {
    const vendorAuthId = request.user.id;
    const page = parseInt(request.query.page as string) || 1;
    const limit = parseInt(request.query.limit as string) || 10;

    const result = await getVendorPayoutHistoryService(vendorAuthId, page, limit);

    return response.success(
      {
        data: { payouts: result.payouts },
        meta: {
          total: result.total,
          pages: result.pages,
          page: result.page,
          limit: result.limit,
        },
      },
      { message: "Payout history fetched successfully" },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get vendor's transaction history
 */
export async function getTransactionHistory(request: Request, response: Response) {
  try {
    const vendorAuthId = request.user.id;
    const page = parseInt(request.query.page as string) || 1;
    const limit = parseInt(request.query.limit as string) || 10;

    const result = await getVendorTransactionHistoryService(vendorAuthId, page, limit);

    return response.success(
      {
        data: { transactions: result.payments },
        meta: {
          total: result.total,
          pages: result.pages,
          page: result.page,
          limit: result.limit,
        },
      },
      { message: "Transaction history fetched successfully" },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}
