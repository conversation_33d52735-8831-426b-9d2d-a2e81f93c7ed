import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  initializePaymentService,
  verifyPaymentService,
  getPaymentStatsService,
} from "~/services/payments";
import { PaystackService } from "~/services/paystack";

/**
 * Initialize payment for an order
 */
export async function initializePayment(request: Request, response: Response) {
  try {
    const { products, deliveryOption, customerEmail } = request.body;
    const userId = request.user.id;

    if (!products || !Array.isArray(products) || products.length === 0) {
      return response.badRequest({}, { message: "Products are required" });
    }

    if (!deliveryOption) {
      return response.badRequest({}, { message: "Delivery option is required" });
    }

    if (!customerEmail) {
      return response.badRequest({}, { message: "Customer email is required" });
    }

    const result = await initializePaymentService({
      userId,
      products,
      deliveryOption,
      customerEmail,
    });

    return response.success(
      { data: result },
      { message: "Payment initialized successfully" },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Verify payment status
 */
export async function verifyPayment(request: Request, response: Response) {
  try {
    const { reference } = request.params;

    if (!reference) {
      return response.badRequest({}, { message: "Payment reference is required" });
    }

    const result = await verifyPaymentService(reference);

    return response.success(
      { data: result },
      { 
        message: result.alreadyVerified 
          ? "Payment already verified" 
          : "Payment verified successfully" 
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Handle Paystack webhooks
 */
export async function handleWebhook(request: Request, response: Response) {
  try {
    const signature = request.headers["x-paystack-signature"] as string;
    const payload = JSON.stringify(request.body);

    // Verify webhook signature
    if (!PaystackService.verifyWebhookSignature(payload, signature)) {
      return response.unauthorized({}, { message: "Invalid webhook signature" });
    }

    const event = request.body;

    switch (event.event) {
      case "charge.success":
        // Handle successful payment
        await handleSuccessfulPayment(event.data);
        break;

      case "charge.failed":
        // Handle failed payment
        await handleFailedPayment(event.data);
        break;

      case "transfer.success":
        // Handle successful transfer (payout)
        await handleSuccessfulTransfer(event.data);
        break;

      case "transfer.failed":
        // Handle failed transfer
        await handleFailedTransfer(event.data);
        break;

      default:
        console.log(`Unhandled webhook event: ${event.event}`);
    }

    return response.success({}, { message: "Webhook processed successfully" });
  } catch (error) {
    console.error("Webhook processing error:", error);
    return response.internalServerError({}, { message: "Webhook processing failed" });
  }
}

/**
 * Get supported banks
 */
export async function getBanks(request: Request, response: Response) {
  try {
    const { country = "south africa" } = request.query;

    const banks = await PaystackService.getBanks(country as string);

    return response.success(
      { data: { banks } },
      { message: "Banks fetched successfully" },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Verify account number
 */
export async function verifyAccountNumber(request: Request, response: Response) {
  try {
    const { accountNumber, bankCode } = request.body;

    if (!accountNumber || !bankCode) {
      return response.badRequest(
        {},
        { message: "Account number and bank code are required" },
      );
    }

    const accountDetails = await PaystackService.verifyAccountNumber(
      accountNumber,
      bankCode,
    );

    return response.success(
      { data: accountDetails },
      { message: "Account verified successfully" },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

/**
 * Get payment statistics (Admin only)
 */
export async function getPaymentStats(request: Request, response: Response) {
  try {
    const stats = await getPaymentStatsService();

    return response.success(
      { data: stats },
      { message: "Payment statistics fetched successfully" },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

// Helper functions for webhook event handling

async function handleSuccessfulPayment(data: any) {
  try {
    const { reference } = data;
    await verifyPaymentService(reference);
    console.log(`Payment successful for reference: ${reference}`);
  } catch (error) {
    console.error("Error handling successful payment:", error);
  }
}

async function handleFailedPayment(data: any) {
  try {
    const { reference } = data;
    // Update payment status to failed if needed
    console.log(`Payment failed for reference: ${reference}`);
  } catch (error) {
    console.error("Error handling failed payment:", error);
  }
}

async function handleSuccessfulTransfer(data: any) {
  try {
    const { reference } = data;
    // Update payout status to success
    console.log(`Transfer successful for reference: ${reference}`);
  } catch (error) {
    console.error("Error handling successful transfer:", error);
  }
}

async function handleFailedTransfer(data: any) {
  try {
    const { reference } = data;
    // Update payout status to failed
    console.log(`Transfer failed for reference: ${reference}`);
  } catch (error) {
    console.error("Error handling failed transfer:", error);
  }
}
