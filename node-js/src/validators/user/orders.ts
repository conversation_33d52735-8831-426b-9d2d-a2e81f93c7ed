import { DeliveryOption, OrderStatus } from "@prisma/client";
import * as zod from "zod";

const getOrdersQuerySchema = zod.object({
  page: zod.coerce
    .number({
      message: "Page must be a number",
    })
    .int({
      message: "Page must be an integer",
    })
    .min(1, {
      message: "Page must be a positive number",
    })
    .default(1),
  limit: zod.coerce
    .number({
      message: "Limit must be a number",
    })
    .int({
      message: "Limit must be an integer",
    })
    .min(1, {
      message: "Limit must be a positive number",
    })
    .default(10),
  sort: zod
    .enum(["LATEST", "OLDEST"], {
      message: "Sort must be one of 'LATEST', 'OLDEST'",
    })
    .default("LATEST"),
  status: zod
    .enum(
      [
        OrderStatus.PENDING,
        OrderStatus.PROCESSING,
        OrderStatus.READY,
        OrderStatus.COMPLETED,
      ],
      {
        message:
          "Status must be one of 'PENDING', 'PROCESSING', 'READY', 'COMPLETED'",
      }
    )
    .optional(),
  productName: zod
    .string({
      message: "Product name must be a string",
    })
    .min(1, {
      message: "Product name must be at least 1 character long",
    })
    .optional(),
  vendorName: zod
    .string({
      message: "Vendor name must be a string",
    })
    .min(1, {
      message: "Vendor name must be at least 1 character long",
    })
    .optional(),
  minTotalPrice: zod.coerce
    .number({
      message: "Minimum total price must be a number",
    })
    .min(0, {
      message: "Minimum total price must be a non-negative number",
    })
    .optional(),
  maxTotalPrice: zod.coerce
    .number({
      message: "Maximum total price must be a number",
    })
    .min(0, {
      message: "Maximum total price must be a non-negative number",
    })
    .optional(),
  categoryId: zod
    .string({
      message: "Category ID must be a string",
    })
    .length(24, {
      message: "Category ID must be a 24-character string",
    })
    .optional(),
});

const getOrderParamsSchema = zod.object({
  id: zod
    .string({
      message: "ID must be a string",
    })
    .length(24, {
      message: "ID must be a 24-character string",
    }),
});

const createOrderBodySchema = zod.object({
  products: zod.array(
    zod.object({
      productId: zod
        .string({
          message: "Product ID must be a string",
        })
        .length(24, {
          message: "Product ID must be a 24-character string",
        }),
      quantity: zod
        .number({
          message: "Quantity must be a number",
        })
        .int({
          message: "Quantity must be an integer",
        })
        .min(1, {
          message: "Quantity must be a positive number",
        }),
    })
  ),
  deliveryOption: zod
    .enum([DeliveryOption.SELF_PICKUP, DeliveryOption.LOGISTIC], {
      message: "Delivery option must be one of 'SELF_PICKUP', 'LOGISTIC'",
    })
    .default(DeliveryOption.SELF_PICKUP),
  customerEmail: zod
    .string({
      message: "Customer email must be a string",
    })
    .email({
      message: "Customer email must be a valid email address",
    }),
});

export { getOrdersQuerySchema, getOrderParamsSchema, createOrderBodySchema };
