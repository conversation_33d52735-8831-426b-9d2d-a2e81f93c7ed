import { DeliveryOption } from "@prisma/client";
import * as zod from "zod";

export const initializePaymentBodySchema = zod.object({
  products: zod.array(
    zod.object({
      productId: zod
        .string({
          message: "Product ID must be a string",
        })
        .length(24, {
          message: "Product ID must be a 24-character string",
        }),
      quantity: zod
        .number({
          message: "Quantity must be a number",
        })
        .int({
          message: "Quantity must be an integer",
        })
        .min(1, {
          message: "Quantity must be a positive number",
        }),
    }),
  ),
  deliveryOption: zod
    .enum([DeliveryOption.SELF_PICKUP, DeliveryOption.LOGISTIC], {
      message: "Delivery option must be one of 'SELF_PICKUP', 'LOGISTIC'",
    })
    .default(DeliveryOption.SELF_PICKUP),
  customerEmail: zod
    .string({
      message: "Customer email must be a string",
    })
    .email({
      message: "Customer email must be a valid email address",
    }),
});

export const verifyPaymentParamsSchema = zod.object({
  reference: zod
    .string({
      message: "Payment reference must be a string",
    })
    .min(1, {
      message: "Payment reference is required",
    }),
});

export const verifyAccountBodySchema = zod.object({
  accountNumber: zod
    .string({
      message: "Account number must be a string",
    })
    .min(10, {
      message: "Account number must be at least 10 characters",
    })
    .max(10, {
      message: "Account number must be at most 10 characters",
    }),
  bankCode: zod
    .string({
      message: "Bank code must be a string",
    })
    .min(3, {
      message: "Bank code must be at least 3 characters",
    }),
});

export const getBanksQuerySchema = zod.object({
  country: zod
    .string({
      message: "Country must be a string",
    })
    .default("south africa"),
});
