import { BadResponse, NotFoundResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { PaystackService } from "~/services/paystack";

export interface CreateSubaccountData {
  businessName: string;
  settlementBank: string;
  accountNumber: string;
}

export interface VendorPaymentStats {
  totalEarnings: number;
  pendingPayouts: number;
  completedPayouts: number;
  totalOrders: number;
  completedOrders: number;
}

/**
 * Create Paystack subaccount for vendor
 */
export async function createVendorSubaccountService(
  vendorAuthId: string,
  data: CreateSubaccountData,
) {
  const { businessName, settlementBank, accountNumber } = data;

  // Get vendor
  const vendor = await prisma.vendor.findUnique({
    where: { authId: vendorAuthId },
    include: {
      paystackSubaccount: true,
    },
  });

  if (!vendor) {
    throw new NotFoundResponse("Vendor not found");
  }

  if (vendor.paystackSubaccount) {
    throw new BadResponse("Paystack account already connected");
  }

  // Verify account number with bank
  try {
    await PaystackService.verifyAccountNumber(accountNumber, settlementBank);
  } catch (error) {
    throw new BadResponse("Invalid account number or bank code");
  }

  // Create subaccount with Paystack
  const paystackResponse = await PaystackService.createSubaccount({
    business_name: businessName,
    settlement_bank: settlementBank,
    account_number: accountNumber,
    percentage_charge: 10.0, // Platform takes 10%
  });

  if (!paystackResponse.status) {
    throw new BadResponse("Failed to create Paystack subaccount");
  }

  // Save subaccount to database
  const subaccount = await prisma.paystackSubaccount.create({
    data: {
      subaccountCode: paystackResponse.data.subaccount_code,
      businessName,
      settlementBank,
      accountNumber,
      vendorId: vendor.id,
      status: "ACTIVE",
    },
  });

  return {
    subaccount,
    paystackData: paystackResponse.data,
  };
}

/**
 * Get vendor's Paystack subaccount details
 */
export async function getVendorSubaccountService(vendorAuthId: string) {
  const vendor = await prisma.vendor.findUnique({
    where: { authId: vendorAuthId },
    include: {
      paystackSubaccount: true,
    },
  });

  if (!vendor) {
    throw new NotFoundResponse("Vendor not found");
  }

  return vendor.paystackSubaccount;
}

/**
 * Get vendor payment statistics
 */
export async function getVendorPaymentStatsService(
  vendorAuthId: string,
): Promise<VendorPaymentStats> {
  const vendor = await prisma.vendor.findUnique({
    where: { authId: vendorAuthId },
    select: { id: true },
  });

  if (!vendor) {
    throw new NotFoundResponse("Vendor not found");
  }

  // Get all orders for this vendor
  const orders = await prisma.order.findMany({
    where: {
      orderToProduct: {
        some: {
          product: {
            vendorId: vendor.id,
          },
        },
      },
    },
    include: {
      payment: true,
    },
  });

  // Get payouts for this vendor
  const payouts = await prisma.payout.findMany({
    where: { vendorId: vendor.id },
  });

  const totalOrders = orders.length;
  const completedOrders = orders.filter((order) => order.status === "COMPLETED").length;

  const totalEarnings = payouts
    .filter((payout) => payout.status === "SUCCESS")
    .reduce((sum, payout) => sum + payout.amount, 0);

  const pendingPayouts = payouts.filter((payout) => payout.status === "PENDING").length;
  const completedPayouts = payouts.filter((payout) => payout.status === "SUCCESS").length;

  return {
    totalEarnings,
    pendingPayouts,
    completedPayouts,
    totalOrders,
    completedOrders,
  };
}

/**
 * Get vendor's payout history
 */
export async function getVendorPayoutHistoryService(
  vendorAuthId: string,
  page = 1,
  limit = 10,
) {
  const vendor = await prisma.vendor.findUnique({
    where: { authId: vendorAuthId },
    select: { id: true },
  });

  if (!vendor) {
    throw new NotFoundResponse("Vendor not found");
  }

  const skip = (page - 1) * limit;

  const [payouts, total] = await Promise.all([
    prisma.payout.findMany({
      where: { vendorId: vendor.id },
      include: {
        payment: {
          include: {
            order: {
              include: {
                orderToProduct: {
                  include: {
                    product: {
                      select: {
                        name: true,
                        price: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    }),
    prisma.payout.count({
      where: { vendorId: vendor.id },
    }),
  ]);

  const pages = Math.ceil(total / limit);

  return {
    payouts,
    total,
    pages,
    page,
    limit,
  };
}

/**
 * Get vendor's transaction history
 */
export async function getVendorTransactionHistoryService(
  vendorAuthId: string,
  page = 1,
  limit = 10,
) {
  const vendor = await prisma.vendor.findUnique({
    where: { authId: vendorAuthId },
    select: { id: true },
  });

  if (!vendor) {
    throw new NotFoundResponse("Vendor not found");
  }

  const skip = (page - 1) * limit;

  // Get payments for orders containing vendor's products
  const [payments, total] = await Promise.all([
    prisma.payment.findMany({
      where: {
        order: {
          orderToProduct: {
            some: {
              product: {
                vendorId: vendor.id,
              },
            },
          },
        },
      },
      include: {
        order: {
          include: {
            user: {
              select: {
                name: true,
                pictureId: true,
              },
            },
            orderToProduct: {
              include: {
                product: {
                  select: {
                    name: true,
                    price: true,
                  },
                },
              },
            },
          },
        },
        payouts: {
          where: { vendorId: vendor.id },
        },
      },
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    }),
    prisma.payment.count({
      where: {
        order: {
          orderToProduct: {
            some: {
              product: {
                vendorId: vendor.id,
              },
            },
          },
        },
      },
    }),
  ]);

  const pages = Math.ceil(total / limit);

  return {
    payments,
    total,
    pages,
    page,
    limit,
  };
}

/**
 * Update vendor subaccount status
 */
export async function updateVendorSubaccountStatusService(
  vendorAuthId: string,
  status: "ACTIVE" | "INACTIVE",
) {
  const vendor = await prisma.vendor.findUnique({
    where: { authId: vendorAuthId },
    include: {
      paystackSubaccount: true,
    },
  });

  if (!vendor) {
    throw new NotFoundResponse("Vendor not found");
  }

  if (!vendor.paystackSubaccount) {
    throw new BadResponse("No Paystack subaccount found");
  }

  const updatedSubaccount = await prisma.paystackSubaccount.update({
    where: { id: vendor.paystackSubaccount.id },
    data: { status },
  });

  return updatedSubaccount;
}
