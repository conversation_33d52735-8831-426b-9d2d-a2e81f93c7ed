import type { DeliveryOption } from "@prisma/client";

import { BadResponse, NotFoundResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { publicSelector } from "~/selectors/public";
import { vendorSelector } from "~/selectors/vendor";

import { PaystackService } from "./paystack";

export interface CreatePaymentData {
  userId: string;
  products: { productId: string; quantity: number }[];
  deliveryOption: DeliveryOption;
  customerEmail: string;
}

export interface InitializePaymentResponse {
  authorization_url: string;
  access_code: string;
  reference: string;
  order: any;
}

/**
 * Initialize payment for an order
 */
export async function initializePaymentService(
  data: CreatePaymentData,
): Promise<InitializePaymentResponse> {
  const { userId, products: productsForOrder, deliveryOption, customerEmail } = data;

  // Validate products and calculate total
  const products = await prisma.product.findMany({
    where: {
      id: {
        in: productsForOrder.map((product) => product.productId),
      },
      isDeleted: false,
      category: {
        status: "APPROVED",
        isDeleted: false,
      },
      vendor: {
        auth: {
          status: "APPROVED",
          isVerified: true,
          isDeleted: false,
        },
      },
    },
    select: {
      ...publicSelector.product,
      category: {
        select: {
          ...publicSelector.category,
        },
      },
      vendor: {
        select: {
          ...vendorSelector.profile,
          paystackSubaccount: {
            select: {
              subaccountCode: true,
              status: true,
            },
          },
        },
      },
    },
  });

  if (products.length !== productsForOrder.length) {
    throw new BadResponse("Some products are not available");
  }

  // Validate stock
  for (const productForOrder of productsForOrder) {
    const product = products.find((p) => p.id === productForOrder.productId);
    if (product && product.stock < productForOrder.quantity) {
      throw new BadResponse(`Insufficient stock for product: ${product.name}`);
    }
  }

  // Ensure all products are from the same vendor
  const vendorIds = new Set(products.map((product) => product.vendor.id));
  if (vendorIds.size > 1) {
    throw new BadResponse("All products must be from the same vendor");
  }

  const vendor = products[0].vendor;

  // Check if vendor has active Paystack subaccount
  if (!vendor.paystackSubaccount || vendor.paystackSubaccount.status !== "ACTIVE") {
    throw new BadResponse("Vendor payment account is not set up");
  }

  // Calculate total price
  const totalPrice = products.reduce((total, product) => {
    const productForOrder = productsForOrder.find(
      (p) => p.productId === product.id,
    );
    return total + (productForOrder?.quantity || 1) * product.price;
  }, 0);

  // Get user
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: { id: true },
  });

  if (!user) {
    throw new BadResponse("User not found");
  }

  // Create order and payment in transaction
  const result = await prisma.$transaction(async (tx) => {
    // Create order
    const order = await tx.order.create({
      data: {
        userId: user.id,
        totalPrice,
        deliveryOption,
      },
      select: {
        ...publicSelector.order,
      },
    });

    // Create order products
    await tx.orderToProduct.createMany({
      data: products.map((product) => {
        const productForOrder = productsForOrder.find(
          (p) => p.productId === product.id,
        );
        if (!productForOrder) {
          throw new BadResponse("Product order data missing");
        }
        return {
          orderId: order.id,
          productId: product.id,
          quantity: productForOrder.quantity,
        };
      }),
    });

    // Generate payment reference
    const reference = PaystackService.generateReference();

    // Create payment record
    const payment = await tx.payment.create({
      data: {
        reference,
        amount: totalPrice,
        orderId: order.id,
      },
    });

    // Initialize Paystack transaction
    const paystackResponse = await PaystackService.initializeTransaction({
      email: customerEmail,
      amount: PaystackService.convertToKobo(totalPrice),
      currency: "ZAR",
      reference,
      subaccount: vendor.paystackSubaccount!.subaccountCode,
      metadata: {
        orderId: order.id,
        userId: user.id,
        vendorId: vendor.id,
      },
    });

    return {
      ...paystackResponse.data,
      order,
    };
  });

  return result;
}

/**
 * Verify payment and update order status
 */
export async function verifyPaymentService(reference: string) {
  // Get payment record
  const payment = await prisma.payment.findUnique({
    where: { reference },
    include: {
      order: {
        include: {
          orderToProduct: {
            include: {
              product: true,
            },
          },
        },
      },
    },
  });

  if (!payment) {
    throw new NotFoundResponse("Payment not found");
  }

  if (payment.status === "SUCCESS") {
    return { payment, alreadyVerified: true };
  }

  // Verify with Paystack
  const paystackResponse = await PaystackService.verifyTransaction(reference);

  if (!paystackResponse.status) {
    throw new BadResponse("Payment verification failed");
  }

  const { data: transactionData } = paystackResponse;

  // Update payment and order in transaction
  const updatedPayment = await prisma.$transaction(async (tx) => {
    // Update payment status
    const updatedPayment = await tx.payment.update({
      where: { id: payment.id },
      data: {
        status: transactionData.status === "success" ? "SUCCESS" : "FAILED",
        method: transactionData.channel as any,
        gatewayResponse: transactionData,
        paidAt: transactionData.status === "success" ? new Date(transactionData.paid_at) : null,
      },
      include: {
        order: {
          include: {
            orderToProduct: {
              include: {
                product: {
                  include: {
                    vendor: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (transactionData.status === "success") {
      // Update order status to PROCESSING
      await tx.order.update({
        where: { id: payment.orderId },
        data: { status: "PROCESSING" },
      });

      // Reduce product stock
      for (const orderProduct of payment.order.orderToProduct) {
        await tx.product.update({
          where: { id: orderProduct.productId },
          data: {
            stock: {
              decrement: orderProduct.quantity,
            },
          },
        });
      }
    }

    return updatedPayment;
  });

  return { payment: updatedPayment, alreadyVerified: false };
}

/**
 * Process payout to vendor after order completion
 */
export async function processVendorPayoutService(orderId: string) {
  const order = await prisma.order.findUnique({
    where: { id: orderId },
    include: {
      payment: true,
      orderToProduct: {
        include: {
          product: {
            include: {
              vendor: {
                include: {
                  paystackSubaccount: true,
                },
              },
            },
          },
        },
      },
    },
  });

  if (!order) {
    throw new NotFoundResponse("Order not found");
  }

  if (order.status !== "COMPLETED") {
    throw new BadResponse("Order must be completed before payout");
  }

  if (!order.payment || order.payment.status !== "SUCCESS") {
    throw new BadResponse("Payment not successful");
  }

  // Check if payout already exists
  const existingPayout = await prisma.payout.findFirst({
    where: { paymentId: order.payment.id },
  });

  if (existingPayout) {
    throw new BadResponse("Payout already processed");
  }

  const vendor = order.orderToProduct[0].product.vendor;
  if (!vendor.paystackSubaccount) {
    throw new BadResponse("Vendor payment account not set up");
  }

  // Calculate payout amount (90% of payment)
  const payoutAmount = order.payment.amount * 0.9;

  // Generate payout reference
  const reference = PaystackService.generateReference("PAYOUT");

  // Create payout record
  const payout = await prisma.payout.create({
    data: {
      reference,
      amount: payoutAmount,
      paymentId: order.payment.id,
      vendorId: vendor.id,
      status: "PENDING",
    },
  });

  // Note: In a real implementation, you would initiate the transfer here
  // For now, we'll mark it as processing and handle actual transfers separately
  await prisma.payout.update({
    where: { id: payout.id },
    data: { status: "PROCESSING" },
  });

  return payout;
}

/**
 * Get payment statistics for admin dashboard
 */
export async function getPaymentStatsService() {
  const [totalPayments, successfulPayments, pendingPayouts, completedPayouts] =
    await Promise.all([
      prisma.payment.count(),
      prisma.payment.count({ where: { status: "SUCCESS" } }),
      prisma.payout.count({ where: { status: "PENDING" } }),
      prisma.payout.count({ where: { status: "SUCCESS" } }),
    ]);

  const totalRevenue = await prisma.payment.aggregate({
    where: { status: "SUCCESS" },
    _sum: { amount: true },
  });

  const platformRevenue = await prisma.payout.aggregate({
    where: { status: "SUCCESS" },
    _sum: { amount: true },
  });

  return {
    totalPayments,
    successfulPayments,
    pendingPayouts,
    completedPayouts,
    totalRevenue: totalRevenue._sum.amount || 0,
    platformRevenue: (totalRevenue._sum.amount || 0) - (platformRevenue._sum.amount || 0),
  };
}
