import axios from "axios";
import crypto from "crypto";

import { env } from "~/lib/env";

const PAYSTACK_BASE_URL = "https://api.paystack.co";

// Create axios instance for Paystack API
const paystackApi = axios.create({
  baseURL: PAYSTACK_BASE_URL,
  headers: {
    Authorization: `Bearer ${env.PAYSTACK_SECRET_KEY}`,
    "Content-Type": "application/json",
  },
});

export interface PaystackBank {
  id: number;
  name: string;
  slug: string;
  code: string;
  longcode: string;
  gateway: string;
  pay_with_bank: boolean;
  active: boolean;
  country: string;
  currency: string;
  type: string;
  is_deleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PaystackSubaccountData {
  business_name: string;
  settlement_bank: string;
  account_number: string;
  percentage_charge: number;
}

export interface PaystackSubaccountResponse {
  status: boolean;
  message: string;
  data: {
    subaccount_code: string;
    business_name: string;
    description: string;
    primary_contact_name: string | null;
    primary_contact_email: string | null;
    primary_contact_phone: string | null;
    metadata: any;
    percentage_charge: number;
    settlement_bank: string;
    account_number: string;
    settlement_schedule: string;
    active: boolean;
    migrate: boolean;
    id: number;
    createdAt: string;
    updatedAt: string;
  };
}

export interface PaystackTransactionData {
  email: string;
  amount: number; // Amount in kobo/cents
  currency?: string;
  reference?: string;
  callback_url?: string;
  plan?: string;
  invoice_limit?: number;
  metadata?: any;
  channels?: string[];
  split_code?: string;
  subaccount?: string;
  transaction_charge?: number;
  bearer?: string;
}

export interface PaystackTransactionResponse {
  status: boolean;
  message: string;
  data: {
    authorization_url: string;
    access_code: string;
    reference: string;
  };
}

export interface PaystackVerificationResponse {
  status: boolean;
  message: string;
  data: {
    id: number;
    domain: string;
    status: string;
    reference: string;
    amount: number;
    message: string | null;
    gateway_response: string;
    paid_at: string;
    created_at: string;
    channel: string;
    currency: string;
    ip_address: string;
    metadata: any;
    log: any;
    fees: number;
    fees_split: any;
    authorization: any;
    customer: any;
    plan: any;
    split: any;
    order_id: any;
    paidAt: string;
    createdAt: string;
    requested_amount: number;
    pos_transaction_data: any;
    source: any;
    fees_breakdown: any;
    transaction_date: string;
    plan_object: any;
    subaccount: any;
  };
}

export interface PaystackTransferRecipientData {
  type: string;
  name: string;
  account_number: string;
  bank_code: string;
  currency?: string;
}

export interface PaystackTransferRecipientResponse {
  status: boolean;
  message: string;
  data: {
    active: boolean;
    createdAt: string;
    currency: string;
    domain: string;
    id: number;
    integration: number;
    name: string;
    recipient_code: string;
    type: string;
    updatedAt: string;
    is_deleted: boolean;
    details: {
      authorization_code: string | null;
      account_number: string;
      account_name: string | null;
      bank_code: string;
      bank_name: string;
    };
  };
}

export interface PaystackTransferData {
  source: string;
  amount: number;
  recipient: string;
  reason?: string;
  currency?: string;
  reference?: string;
}

export interface PaystackTransferResponse {
  status: boolean;
  message: string;
  data: {
    integration: number;
    domain: string;
    amount: number;
    currency: string;
    source: string;
    reason: string;
    recipient: number;
    status: string;
    transfer_code: string;
    id: number;
    createdAt: string;
    updatedAt: string;
  };
}

/**
 * Paystack Service Class
 */
export class PaystackService {
  /**
   * Get list of supported banks
   */
  static async getBanks(country = "south africa"): Promise<PaystackBank[]> {
    try {
      const response = await paystackApi.get(`/bank?country=${country}`);
      return response.data.data;
    } catch (error) {
      console.error("Error fetching banks:", error);
      throw new Error("Failed to fetch banks");
    }
  }

  /**
   * Verify account number with bank
   */
  static async verifyAccountNumber(
    accountNumber: string,
    bankCode: string,
  ): Promise<{ account_name: string; account_number: string }> {
    try {
      const response = await paystackApi.get(
        `/bank/resolve?account_number=${accountNumber}&bank_code=${bankCode}`,
      );
      return response.data.data;
    } catch (error) {
      console.error("Error verifying account:", error);
      throw new Error("Failed to verify account number");
    }
  }

  /**
   * Create a subaccount for vendor
   */
  static async createSubaccount(
    data: PaystackSubaccountData,
  ): Promise<PaystackSubaccountResponse> {
    try {
      const response = await paystackApi.post("/subaccount", data);
      return response.data;
    } catch (error) {
      console.error("Error creating subaccount:", error);
      throw new Error("Failed to create subaccount");
    }
  }

  /**
   * Initialize a transaction
   */
  static async initializeTransaction(
    data: PaystackTransactionData,
  ): Promise<PaystackTransactionResponse> {
    try {
      const response = await paystackApi.post("/transaction/initialize", data);
      return response.data;
    } catch (error) {
      console.error("Error initializing transaction:", error);
      throw new Error("Failed to initialize transaction");
    }
  }

  /**
   * Verify a transaction
   */
  static async verifyTransaction(
    reference: string,
  ): Promise<PaystackVerificationResponse> {
    try {
      const response = await paystackApi.get(
        `/transaction/verify/${reference}`,
      );
      return response.data;
    } catch (error) {
      console.error("Error verifying transaction:", error);
      throw new Error("Failed to verify transaction");
    }
  }

  /**
   * Create transfer recipient
   */
  static async createTransferRecipient(
    data: PaystackTransferRecipientData,
  ): Promise<PaystackTransferRecipientResponse> {
    try {
      const response = await paystackApi.post("/transferrecipient", data);
      return response.data;
    } catch (error) {
      console.error("Error creating transfer recipient:", error);
      throw new Error("Failed to create transfer recipient");
    }
  }

  /**
   * Initiate transfer to vendor
   */
  static async initiateTransfer(
    data: PaystackTransferData,
  ): Promise<PaystackTransferResponse> {
    try {
      const response = await paystackApi.post("/transfer", data);
      return response.data;
    } catch (error) {
      console.error("Error initiating transfer:", error);
      throw new Error("Failed to initiate transfer");
    }
  }

  /**
   * Verify webhook signature
   */
  static verifyWebhookSignature(payload: string, signature: string): boolean {
    if (!env.PAYSTACK_WEBHOOK_SECRET) {
      console.warn("Webhook secret not configured");
      return false;
    }

    const hash = crypto
      .createHmac("sha512", env.PAYSTACK_WEBHOOK_SECRET)
      .update(payload)
      .digest("hex");

    return hash === signature;
  }

  /**
   * Generate unique payment reference
   */
  static generateReference(prefix = "ECO"): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Convert amount to kobo/cents (Paystack expects amounts in smallest currency unit)
   */
  static convertToKobo(amount: number): number {
    return Math.round(amount * 100);
  }

  /**
   * Convert amount from kobo/cents to main currency unit
   */
  static convertFromKobo(amount: number): number {
    return amount / 100;
  }
}
